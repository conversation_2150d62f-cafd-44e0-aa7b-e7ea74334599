import { format, eachDayOfInterval, startOfMonth, endOfMonth, isSameMonth, getDay } from 'date-fns';
import { Trade } from '../types/trade';
import { Theme } from '@mui/material';

export type TimePeriod = 'month' | 'year' | 'all';

export interface ChartDataPoint {
  date: string;
  pnl: number;
  cumulativePnL: number;
  isIncreasing: boolean;
  isDecreasing: boolean;
  dailyChange: number;
  isWin: boolean;
  isLoss: boolean;
  isBreakEven: boolean;
  trades: Trade[];
  fullDate: Date;
}

export interface SessionStats {
  session: string;
  totalTrades: number;
  winners: number;
  losers: number;
  breakevens: number;
  winRate: number;
  totalPnL: number;
  averagePnL: number;
  pnlPercentage: number;
}

// Function to filter trades based on selected time period
export const getFilteredTrades = (trades: Trade[], selectedDate: Date, period: TimePeriod): Trade[] => {
  switch (period) {
    case 'month':
      return trades.filter(trade => isSameMonth(new Date(trade.date), selectedDate));
    case 'year':
      return trades.filter(trade => new Date(trade.date).getFullYear() === selectedDate.getFullYear());
    case 'all':
      return trades;
    default:
      return trades;
  }
};
export const getTradesStats = (trades: Trade[]) => {

  // Create a map to store stats for each tag
  const stats = { wins: 0, losses: 0, breakevens: 0, totalPnL: 0 };
  trades.forEach(trade => {
    if (trade.type === 'win') {
      stats.wins++;
    } else if (trade.type === 'loss') {
      stats.losses++;
    } else if (trade.type === 'breakeven') {
      stats.breakevens++;
    }
    stats.totalPnL += trade.amount;
  });

  // Calculate win rate excluding breakevens from the denominator
  const totalTradesForWinRate = stats.wins + stats.losses;
  const winRate = totalTradesForWinRate > 0 ? Math.round((stats.wins / totalTradesForWinRate) * 100) : 0;
  const totalTrades = stats.wins + stats.losses + stats.breakevens;

  return {
    trades,
    wins: stats.wins,
    losses: stats.losses,
    breakevens: stats.breakevens,
    totalTrades,
    winRate,
    totalPnL: stats.totalPnL
  };

}


export const getTagDayOfWeekChartData = (
trades: Trade[],  theme: Theme, winRateMetric : boolean = true) => {
  // Day of week names
  const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  

  // Group trades by day of week
  const tradesByDay = DAYS_OF_WEEK.map((day, index) => {
    // Get trades for this day of week
    const dayTrades = trades.filter(trade => {
      const tradeDate = new Date(trade.date);
      return getDay(tradeDate) === index;
    });

    // Calculate statistics
    const totalTrades = dayTrades.length;
    const winTrades = dayTrades.filter(trade => trade.type === 'win').length;
    const lossTrades = dayTrades.filter(trade => trade.type === 'loss').length;
    const winRate = totalTrades > 0 ? (winTrades / totalTrades) * 100 : 0;
    const totalPnL = dayTrades.reduce((sum, trade) => sum + trade.amount, 0);

    return {
      day,
      dayIndex: index,
      totalTrades,
      winTrades,
      lossTrades,
      winRate,
      pnl: totalPnL,
      trades: dayTrades
    };
  });
   // Define colors - memoized to prevent unnecessary re-renders
    const COLORS = { 
      neutral: theme.palette.grey[500],
      sunday: '#FF6384',
      monday: '#36A2EB',
      tuesday: '#FFCE56',
      wednesday: '#4BC0C0',
      thursday: '#9966FF',
      friday: '#FF9F40',
      saturday: '#C9CBCF'
    };

  return tradesByDay.map(dayData => ({
      day: dayData.day.substring(0, 3), // Abbreviate day names
      fullDay: dayData.day, 
      totalTrades: dayData.totalTrades,
      winTrades: dayData.winTrades,
      lossTrades: dayData.lossTrades,
      value: winRateMetric? dayData.winRate : dayData.pnl,
      winRate: dayData.winRate,
      pnl: dayData.pnl,
      trades: dayData.trades,
      color: COLORS[dayData.day.toLowerCase() as keyof typeof COLORS] || COLORS.neutral
    }));
 
};


// Calculate chart data for cumulative P&L - async to prevent UI blocking
export const calculateChartData = async (
  trades: Trade[],
  selectedDate: Date,
  timePeriod: TimePeriod
): Promise<ChartDataPoint[]> => {
  const filteredTrades = getFilteredTrades(trades, selectedDate, timePeriod);

  // Yield control to prevent UI blocking
  await new Promise(resolve => setTimeout(resolve, 0));

  // Get the date range for the selected period
  let startDate, endDate;
  if (timePeriod === 'month') {
    startDate = startOfMonth(selectedDate);
    endDate = endOfMonth(selectedDate);
  } else if (timePeriod === 'year') {
    startDate = new Date(selectedDate.getFullYear(), 0, 1);
    endDate = new Date(selectedDate.getFullYear(), 11, 31);
  } else {
    // For 'all', use the first and last trade dates
    if (filteredTrades.length === 0) {
      startDate = new Date();
      endDate = new Date();
    } else {
      const sortedTrades = [...filteredTrades].sort((a, b) =>
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );
      startDate = new Date(sortedTrades[0].date);
      endDate = new Date(sortedTrades[sortedTrades.length - 1].date);
    }
  }

  // Generate an array of all days in the period
  const days = eachDayOfInterval({ start: startDate, end: endDate });

  // Calculate cumulative P&L for each day
  let cumulative = 0;
  let prevCumulative = 0;

  // Process in chunks to prevent blocking for large datasets
  const chunkSize = 100;
  const result: ChartDataPoint[] = [];

  for (let i = 0; i < days.length; i += chunkSize) {
    const chunk = days.slice(i, i + chunkSize);

    // Process each day in the chunk sequentially to avoid unsafe references
    const chunkResult: ChartDataPoint[] = [];
    for (const day of chunk) {
      // Find trades for this day
      const dayTrades = filteredTrades.filter(trade =>
        format(new Date(trade.date), 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
      );

      // Calculate daily P&L
      const dailyPnL = dayTrades.reduce((sum, trade) => sum + trade.amount, 0);

      // Update cumulative P&L
      prevCumulative = cumulative;
      cumulative += dailyPnL;

      chunkResult.push({
        date: format(day, timePeriod === 'month' ? 'MM/dd' : 'MM/dd/yyyy'),
        pnl: dailyPnL,
        cumulativePnL: cumulative,
        isIncreasing: cumulative > prevCumulative,
        isDecreasing: cumulative < prevCumulative,
        dailyChange: cumulative - prevCumulative,
        isWin: dailyPnL > 0,
        isLoss: dailyPnL < 0,
        isBreakEven: dailyPnL === 0,
        trades: dayTrades,
        fullDate: new Date(day)
      });
    }

    result.push(...chunkResult);

    // Yield control after each chunk
    if (i + chunkSize < days.length) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }

  return result;
};

// Calculate session performance statistics
export const calculateSessionStats = (
  trades: Trade[],
  selectedDate: Date,
  timePeriod: TimePeriod,
  accountBalance: number
): SessionStats[] => {
  const filteredTrades = getFilteredTrades(trades, selectedDate, timePeriod).filter(trade => trade.session !== undefined);
  const sessions = ['Asia', 'London', 'NY AM', 'NY PM'];

  return sessions.map(sessionName => {
    const sessionTrades = filteredTrades.filter(trade => trade.session === sessionName);
    const totalTrades = sessionTrades.length;
    const winners = sessionTrades.filter(trade => trade.type === 'win').length;
    const losers = sessionTrades.filter(trade => trade.type === 'loss').length;
    const breakevens = sessionTrades.filter(trade => trade.type === 'breakeven').length;

    // Calculate win rate excluding breakevens from the denominator
    const totalTradesForWinRate = winners + losers;
    const winRate = totalTradesForWinRate > 0 ? (winners / totalTradesForWinRate) * 100 : 0;

    const totalPnL = sessionTrades.reduce((sum, trade) => sum + trade.amount, 0);
    const averagePnL = totalTrades > 0 ? totalPnL / totalTrades : 0;
    const pnlPercentage = accountBalance > 0 ? (totalPnL / accountBalance) * 100 : 0;

    return {
      session: sessionName,
      totalTrades,
      winners,
      losers,
      breakevens,
      winRate,
      totalPnL,
      averagePnL,
      pnlPercentage
    };
  });
};

// Calculate target value for monthly target
export const calculateTargetValue = (monthlyTarget: number | undefined, accountBalance: number): number | null => {
  if (monthlyTarget === undefined || accountBalance <= 0) return null;
  return (monthlyTarget / 100) * accountBalance;
};

// Calculate drawdown violation value
export const calculateDrawdownViolationValue = (maxDailyDrawdown: number, accountBalance: number): number => {
  return -(maxDailyDrawdown / 100) * accountBalance;
};
